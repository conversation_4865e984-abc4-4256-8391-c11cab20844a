import pandas as pd
import json
from typing import Dict, Set


class TextProcessor:
    def __init__(
        self,
        stopwords_filepath: str,
        corpus_filepath: str,
        idx2label_filepath: str
    ) -> None:
        self.stopwords_filepath = stopwords_filepath
        self.corpus_filepath = corpus_filepath
        self.idx2label_filepath = idx2label_filepath

        self.stopwords: Set[str] = self.get_stopwords()
        self.idx2label: Dict[str, str] = self.read_json_content()
        self.corpus: pd.DataFrame = self._load_corpus()
        self.word_freq: Dict[str, int] = self._build_vocab_from_corpus()
        self.word2idx: Dict[str, int] = {w: i for i, w in enumerate(sorted(self.word_freq.keys()))}

    def get_stopwords(self):
        """
        Load and return the set of stopwords from the stopwords file specified 

        Args: 
            The function requires no input parameter 

        Returns:
            Set[str]: A set containing all stopwords loaded from the file.
        """
        with open(self.stopwords_filepath, "r") as file_ref:
            return set(line.strip().lower() for line in file_ref if line.strip())

    def read_json_content(self):
        """
        Load and return the set of stopwords from the stopwords json file  

        Args: 
            The function requires no input parameter 

        Returns:
            Set[str]: A set containing all stopwords loaded from the file.
        """
        with open(self.idx2label_filepath, "r") as file_ref:
            return json.load(file_ref)

    def _clean_text(self, text: str):
        """
        Remove any digits, special characters, stop word 
        and word with less than 2 letters from input string   

        Args: 
            text (str): The input string to be formatted 

        Returns:
            List[str]: A list of words that are valide
        """
        text = text.lower()
        cleaned = ""
        for char in text:
            if char.isalpha():
                cleaned += char
            else:
                cleaned += " "
        
        words =[]
        for word in cleaned.split():
            if word not in self.stopwords and len(word) >= 2:
                words.append(word)

        return words

    def _load_corpus(self):
        """
        Load and read data from corpus csv file   

        Args: 
            The function requires no input parameter 

        Returns:
            List[str]: A list of words that are valide
        """
        data_frame = pd.read_csv(self.corpus_filepath)
        if "label" in data_frame.columns and "text" in data_frame.columns:
            data_frame["label_name"] = data_frame["label"].map(self.idx2label)
        return data_frame

    def _build_vocab_from_corpus(self):
        """
        Load and read data from corpus csv file   

        Args: 
            The function requires no input parameter 

        Returns:
            List[str]: A list of words that are valide
        """
        freq = {}
        for text in self.corpus["text"]:
            for word in self._clean_text(str(text)):
                freq[word] = freq.get(word, 0) + 1
        return freq

    def _update_word2idx(self):
        self.word2idx = {w: i for i, w in enumerate(sorted(self.word_freq.keys()))}


    def add_file(self, add_file_path: str) -> None:
        data_frame = pd.read_csv(add_file_path)
        if "label" in data_frame.columns and "text" in data_frame.columns:
            data_frame["label_name"] = data_frame["label"].map(self.idx2label)

        self.corpus = pd.concat([self.corpus, data_frame], ignore_index=True)

        for text in data_frame["text"]:
            for word in self._clean_text(str(text)):
                self.word_freq[word] = self.word_freq.get(word, 0) + 1

        self.save()

    def delete_file(self, delete_file_path: str) -> None:
        """
        Delete documents from the corpus and update vocabulary accordingly.
        """
        df = pd.read_csv(delete_file_path)

        if "label" in df.columns and "text" in df.columns:
            df["label_name"] = df["label"].map(self.idx2label)

        original_corpus = self.corpus.copy()
        merged = original_corpus.merge(
            df[["label", "text"]],
            on=["label", "text"],
            how="left",
            indicator=True
        )
        self.corpus = merged[merged["_merge"] == "left_only"].drop(columns=["_merge"]).reset_index(drop=True)

        self.word_freq.clear()
        for text in self.corpus["text"]:
            for word in self._clean_text(str(text)):
                self.word_freq[word] = self.word_freq.get(word, 0) + 1

        self.save()


    def load(self) -> None:
        self.word_freq = {}
        with open("word_freq.txt", "r") as file_ref:
            for line in file_ref:
                word, freq = line.strip().split()
                self.word_freq[word] = int(freq)

        self._update_word2idx()

    def save(self) -> None:
        # Save word_freq.txt
        with open("word_freq.txt", "w") as file_ref:
            for word, count in sorted(self.word_freq.items(), key=lambda x: (-x[1], x[0])):
                file_ref.write(f"{word} {count}\n")

        # Save word2idx.txt
        self._update_word2idx()
        with open("word2idx.txt", "w") as file_ref:
            for word, index in self.word2idx.items():
                file_ref.write(f"{word} {index}\n")

        # Save idx2word.txt
        with open("idx2word.txt", "w") as file_ref:
            for word, index in self.word2idx.items():
                file_ref.write(f"{index} {word}\n")


if __name__ == "__main__":
    tp = TextProcessor(
        stopwords_filepath="data/stop_words_english.txt",
        corpus_filepath="data/ag_news_test.csv",
        idx2label_filepath="data/idx2label.json",
    )
    tp.save()
