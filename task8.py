from typing import Optional
import os
from task7 import TextProcessor


class Role:
    def __init__(self, user_name: str, access: str, name: str):
        """
        Initialize a Role object with user information.

        Args:
            user_name (str): The username for login
            access (str): The access level ("reader" or "admin")
            name (str): The display name of the user
        """
        self.user_name = user_name
        self.access = access
        self.name = name

    def get_user_name(self):
        """Return the username."""
        return self.user_name

    def get_access(self):
        """Return the access level."""
        return self.access

    def get_name(self):
        """Return the display name."""
        return self.name


class RoleBasedVocabSys:
    
    def __init__(
        self,
        users_info,
        stopwords_filepath="data/stop_words_english.txt",
        corpus_filepath="data/ag_news_test.csv",
        idx2label_filepath="data/idx2label.json"
        ):
        """
        Initialize the role-based vocabulary system.

        Args:
            users_info (dict): Dictionary containing user information
            stopwords_filepath (str): Path to stopwords file
            corpus_filepath (str): Path to corpus file
            idx2label_filepath (str): Path to label mapping file
        """
        self.users_info = users_info
        self.current_user = None
        self.text_processor = TextProcessor(
            stopwords_filepath=stopwords_filepath,
            corpus_filepath=corpus_filepath,
            idx2label_filepath=idx2label_filepath
        )
        
    def start(self):
        """
        Start the vocabulary management system with a menu-driven interface.
        """
        print("Welcome to the Mark system v0.0!")

        while True:
            menu = self.generate_menu()
            print(menu)

            choice = self.get_user_choice()

            if choice == "1":  # Exit
                print("Thank you for using the system. Goodbye!")
                break
            elif choice == "2":  # Login/Logout
                if self.current_user is None:
                    self.login()
                else:
                    self.logout()
            elif choice == "3":  # View top 10 frequent words
                self.view_top_words()
            elif choice == "4":  # View bottom 10 frequent words
                self.view_bottom_words()
            elif choice == "5":  # Add file (admin only)
                self.add_file_to_vocab()
            elif choice == "6":  # Remove file (admin only)
                self.remove_file_from_vocab()
            else:
                print("Invalid choice. Please try again.")
          
    def generate_menu(self) -> str:
        """
        Generate menu options based on user login status and access level.

        Returns:
            str: The menu string to display
        """
        menu = "Please select an option:\n"
        menu += "1. Exit\n"

        if self.current_user is None:
            menu += "2. Login\n"
        else:
            menu += f"2. Logout (Currently logged in as: {self.current_user.get_name()})\n"
            menu += "3. Show top 10 frequency words\n"
            menu += "4. Show last 10 frequency words\n"

            # Admin-only options
            if self.current_user.get_access() == "admin":
                menu += "5. Updating vocabulary for adding\n"
                menu += "6. Updating vocabulary for excluding\n"

        return menu
    
    def verify_user_choice(self, user_choice) -> bool:
        """
        Verify if the user choice is valid based on current state.

        Args:
            user_choice (str): The user's menu choice

        Returns:
            bool: True if choice is valid, False otherwise
        """
        valid_choices = ["1", "2"]  # Exit and Login/Logout always available

        if self.current_user is not None:
            valid_choices.extend(["3", "4"])  # View options for logged-in users

            if self.current_user.get_access() == "admin":
                valid_choices.extend(["5", "6"])  # Admin-only options

        return user_choice in valid_choices

    def get_user_choice(self):
        """
        Get and validate user input for menu choice.

        Returns:
            str: Valid user choice
        """
        while True:
            user_choice = input("Enter your choice: ").strip()
            if self.verify_user_choice(user_choice):
                return user_choice
            else:
                print("Invalid choice. Please select a valid option.")
    
    def login(self):
        """
        Handle user login process.
        """
        print("\n--- Login ---")
        username = input("Enter username: ").strip()
        password = input("Enter password: ").strip()

        # Find user with case-insensitive username matching
        found_user = None
        actual_username = None
        for user_key in self.users_info:
            if user_key.lower() == username.lower():
                found_user = self.users_info[user_key]
                actual_username = user_key
                break

        if found_user is not None:
            if found_user["password"] == password:
                self.current_user = Role(
                    user_name=actual_username,
                    access=found_user["role"],
                    name=found_user["name"]
                )
                print("\n" + "="*50)
                print(f"Welcome, {self.current_user.get_name()}!")
            else:
                print("Invalid password. Login failed.")
        else:
            print("Username not found. Login failed.")

    def logout(self):
        """
        Handle user logout process.
        """
        if self.current_user:
            print(f"Goodbye, {self.current_user.get_name()}!")
            self.current_user = None
        else:
            print("No user is currently logged in.")

    def view_top_words(self):
        """
        Display the top 10 most frequent words.
        """
        if self.current_user is None:
            print("Please login first to access this feature.")
            return

        print("\n--- Top 10 Most Frequent Words ---")
        sorted_words = sorted(self.text_processor.word_freq.items(),
                            key=lambda x: (-x[1], x[0]))

        for i, (word, freq) in enumerate(sorted_words[:10], 1):
            print(f"{i:2d}. {word}: {freq}")

    def view_bottom_words(self):
        """
        Display the bottom 10 least frequent words.
        """
        if self.current_user is None:
            print("Please login first to access this feature.")
            return

        print("\n--- Bottom 10 Least Frequent Words ---")
        sorted_words = sorted(self.text_processor.word_freq.items(),
                            key=lambda x: (x[1], x[0]))

        for i, (word, freq) in enumerate(sorted_words[:10], 1):
            print(f"{i:2d}. {word}: {freq}")

    def add_file_to_vocab(self):
        """
        Add a file to the vocabulary (admin only).
        """
        if self.current_user is None or self.current_user.get_access() != "admin":
            print("Access denied. This feature is only available to administrators.")
            return

        print("\n--- Add File to Vocabulary ---")
        file_path = input("Enter file path to add: ").strip()

        try:
            self.text_processor.add_file(file_path)
            print(f"Successfully added file: {file_path}")
            print("Vocabulary has been updated.")
        except Exception as e:
            print(f"Error adding file: {e}")

    def remove_file_from_vocab(self):
        """
        Remove a file from the vocabulary (admin only).
        """
        if self.current_user is None or self.current_user.get_access() != "admin":
            print("Access denied. This feature is only available to administrators.")
            return

        print("\n--- Remove File from Vocabulary ---")
        file_path = input("Enter file path to remove: ").strip()

        try:
            self.text_processor.delete_file(file_path)
            print(f"Successfully removed file: {file_path}")
            print("Vocabulary has been updated.")
        except Exception as e:
            print(f"Error removing file: {e}")
            

if __name__ == "__main__":
    users_info = {
        "Jueqing": {
            "role": "reader",
            "password": "jueqing123",
            "name": "Jueqing Lu"
        },
        "Trang": {
            "role": "admin",
            "password": "trang123",
            "name": "Trang Vu"
        },
        "land": {
            "role": "admin",
            "password": "landu123",
            "name": "Lan Du"
        }
        
    }
    a_sys = RoleBasedVocabSys(
        users_info=users_info,
        stopwords_filepath="data/stop_words_english.txt",  # Using the available file
        corpus_filepath="data/ag_news_test.csv",
        idx2label_filepath="data/idx2label.json"
    )
    a_sys.start()
    
